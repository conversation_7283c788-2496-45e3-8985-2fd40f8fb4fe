<?php

require('../functions.php');
require('functions.php');
super_admin_config();
$account = bxc_cloud_super_account();

?>
<html lang="en-US">
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no" />
    <title>Super | Boxcoin Cloud</title>
    <script>var BXC_TRANSLATIONS = {}, BXC_URL = "<?php echo CLOUD_URL ?>", BXC_ADMIN = true;</script>
    <script src="../js/client<?php echo isset($_GET['debug']) ? '' : '.min' ?>.js?v=<?php echo BXC_VERSION ?>"></script>
    <script src="js/super.js?v=<?php echo BXC_VERSION ?>"></script>
    <link rel="stylesheet" href="../css/admin.css?v=1" type="text/css" media="all" />
    <link rel="shortcut icon" href="../media/icon.svg" />
    <?php bxc_cloud_admin() ?>
    <style>body { min-height: 0; }</style>
</head>
<body>
    <div id="bxc-super-cnt" class="bxc-cloud-box active">
        <?php if ($account) box_cloud_super_main(); else box_cloud_super_login(); ?>
    </div>
</body>
</html>

<?php function box_cloud_super_main() { ?>
<div id="bxc-super" class="bxc-main">
    <div class="bxc-sidebar">
        <div>
            <img class="bxc-logo" src="../media/logo.svg" />
            <img class="bxc-logo-icon" src="../media/icon.svg" />
        </div>
        <div class="bxc-nav">
            <div id="customers" class="bxc-active">
                <i class="bxc-icon-user"></i><span>Customers</span>
            </div>
            <div id="payments" onclick="window.open('<?php echo CLOUD_URL ?>payment/admin.php')">
                <i class="bxc-icon-bar-chart"></i><span>Payments</span>
            </div>
        </div>
    </div>
    <div class="bxc-body">
        <main>
            <div data-area="customers" class="bxc-active">
                <table id="bxc-table-customers" class="bxc-table">
                    <thead>
                        <tr>
                            <th data-field="id"></th>
                            <th data-field="email">
                                Email
                            </th>
                            <th data-field="db">
                                Database
                            </th>
                            <th data-field="creation_time">
                                Creation time
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
          $customers = bxc_cloud_super_get_customers();
          $code = '';
          for ($i = 0; $i < count($customers); $i++) {
              $code .= '<tr data-id="' . $customers[$i]['id'] . '"><td class="bcx-td-id">' . $customers[$i]['id'] . '</td><td class="bcx-td-email">' . $customers[$i]['email'] . '</td><td class="bxc-td-db">' . $customers[$i]['db_name'] . '</td><td class="bxc-td-creation-time">' . $customers[$i]['creation_time'] . '</td></tr>';
          }
          echo $code;
                        ?>
                    </tbody>
                </table>
            </div>
            <div data-area="payments"></div>
        </main>
    </div>
    <div id="bxc-lightbox">
        <div>
            <div class="bxc-top">
                <div class="bxc-title"></div>
                <div>
                    <div class="bxc-lightbox-buttons"></div>
                    <div id="bxc-lightbox-close" class="bxc-btn-icon bxc-btn-red">
                        <i class="bxc-icon-close"></i>
                    </div>
                </div>
            </div>
            <div id="bxc-lightbox-main" class="bxc-scrollbar"></div>
        </div>
        <span></span>
    </div>
    <div id="bxc-lightbox-loading" class="bxc-loading"></div>
</div>
<?php } ?>

<?php function box_cloud_super_login() { ?>
<div id="bxc-super-login" class="bxc-main bxc-box">
    <form>
        <div class="bxc-info"></div>
        <div class="bxc-top">
            <img src="../media/logo.svg" />
            <div class="bxc-title">
                Sign in
            </div>
            <div class="bxc-text">
                To continue to Boxcoin Super Admin area
            </div>
        </div>
        <div id="email" class="bxc-input">
            <span>
                Email
            </span>
            <input type="email" required="" />
        </div>
        <div id="password" class="bxc-input">
            <span>
                Password
            </span>
            <input type="password" required="" />
        </div>
        <div class="bxc-bottom">
            <div id="bxc-super-submit-login" class="bxc-btn">
                Sign in
            </div>
        </div>
    </form>
</div>
<?php } ?>