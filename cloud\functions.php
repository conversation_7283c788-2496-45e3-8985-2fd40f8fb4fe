<?php

/*
 *
 * ===================================================================
 * CLOUD FUNCTIONS FILE
 * ===================================================================
 *
 */

global $CLOUD_CONNECTION;
global $CLOUD_CUSTOMER_CONNECTION;
global $CLOUD_ACTIVE_ACCOUNT;
if (defined('CLOUD_CUSTOM_PHP')) {
    require(CLOUD_CUSTOM_PHP);
}

function bxc_cloud_ajax($function, $arguments = false) {
    switch ($function) {
        case 'registration':
            return bxc_cloud_registration($arguments);
        case 'login':
            return bxc_cloud_login($arguments['email'], $arguments['password'], false, $arguments['code']);
        case 'welcome-email':
            return bxc_cloud_welcome_email();
        case 'reset-password':
            return bxc_cloud_reset_password($arguments['email'], bxc_isset($arguments, 'token'), bxc_isset($arguments, 'password'));
        case 'super-login':
            return bxc_cloud_super_login($arguments['email'], $arguments['password']);
        case 'super-get-customer':
            return bxc_cloud_super_get_customer($arguments['customer_id']);
        case 'super-save-customer':
            return bxc_cloud_super_save_customer($arguments['token'], $arguments['details']);
        case 'customer-lifetime-value':
            return bxc_cloud_payment_history(false, true);
    }
}

/*
 * -----------------------------------------------------------
 * # DATABASE
 * -----------------------------------------------------------
 *
 */

function db_connect() {
    global $CLOUD_CONNECTION;
    if ($CLOUD_CONNECTION) {
        return true;
    }
    $CLOUD_CONNECTION = new mysqli(CLOUD_DB_HOST, CLOUD_DB_USER, CLOUD_DB_PASSWORD, CLOUD_DB_NAME);
    if ($CLOUD_CONNECTION->connect_error) {
        echo 'Connection error';
        return false;
    }
    return true;
}


function db_get($query, $single = true) {
    $status = db_connect();
    $connection = $GLOBALS['CLOUD_CONNECTION'];
    $value = $single ? '' : [];
    if ($status) {
        $result = $connection->query($query);
        if ($result) {
            if ($result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    if ($single) {
                        $value = $row;
                    } else {
                        array_push($value, $row);
                    }
                }
            }
        }
    } else {
        return $status;
    }
    return $value;
}

function db_query($query, $return = false) {
    $status = db_connect();
    $connection = $GLOBALS['CLOUD_CONNECTION'];
    if ($status) {
        $result = $connection->query($query);
        if ($result) {
            if ($return) {
                if (isset($connection->insert_id) && $connection->insert_id > 0) {
                    return $connection->insert_id;
                } else
                    return false;
            } else {
                return true;
            }
        } else {
            return $connection->error;
        }
    } else {
        return $status;
    }
}

function db_escape($value, $numeric = -1) {
    if (is_numeric($value)) {
        return $value;
    } else if ($numeric === true) {
        return false;
    }
    global $CLOUD_CONNECTION;
    db_connect();
    $value = $CLOUD_CONNECTION->real_escape_string($value);
    $value = str_replace(['<script', '</script'], ['&lt;script', '&lt;/script'], $value);
    $value = str_replace(['javascript:', 'onclick=', 'onerror='], '', $value);
    $value = htmlspecialchars($value, ENT_NOQUOTES | ENT_SUBSTITUTE, 'utf-8');
    return str_replace('"', '\"', $value);
}

/*
 * -----------------------------------------------------------
 * # ACCOUNT
 * -----------------------------------------------------------
 *
 */

function bxc_cloud_registration($details) {
    $now = gmdate('Y-m-d H:i:s');
    $token = bin2hex(openssl_random_pseudo_bytes(20));
    $response = false;

    // Validation
    if (strlen($details['password']) < 8) {
        return 'password-length';
    } else if (!strpos($details['email'], '@') || !strpos($details['email'], '.')) {
        return 'invalid-email';
    } else if (intval(db_get('SELECT COUNT(*) as count FROM users WHERE email = "' . db_escape($details['email']) . '"')['count']) > 0) {
        return 'duplicate-email';
    }

    // Cloud user registration
    $user_slug = 'bxc_' . rand(1, ********);
    $databases = array_column(db_get('SHOW DATABASES', false), 'Database');
    while (in_array($user_slug, $databases)) {
        $user_slug = 'bxc_' . rand(1, ********);
    }
    $db_name = $user_slug;
    $response = db_query('INSERT INTO users (email, password, token, last_activity, creation_time, db_name) VALUES ("' . db_escape($details['email']) . '", "' . db_escape(password_hash($details['password'], PASSWORD_DEFAULT)) . '", "' . $token . '", "' . $now . '", "' . $now . '", "' . $db_name . '")', true);
    if (is_int($response)) {
        $db_password = bin2hex(openssl_random_pseudo_bytes(20));

        // Database creation
        $response = db_query('CREATE DATABASE `' . $user_slug . '` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;');
        if ($response === true) {
            if (bxc_cloud_is_localhost()) {
                $user_slug = 'root';
                $db_password = '';
            } else {
                $mysql_user = "'" . $user_slug . "'@'" . (defined('CLOUD_IP') ? CLOUD_IP : 'localhost') . "'";
                db_query('CREATE USER ' . $mysql_user . ' IDENTIFIED BY \'' . $db_password . '\'');
                db_query('GRANT ALL PRIVILEGES ON ' . $user_slug . '.* TO ' . $mysql_user . ' WITH GRANT OPTION');
                db_query('GRANT select, update, delete, insert, create, drop, index, alter, lock tables, execute, create temporary tables, execute, trigger, create view, show view, references, event ON ' . $user_slug . '.* TO ' . $mysql_user);
            }

            // Boxcoin installation
            $response = bxc_installation(['db-name' => $db_name, 'db-user' => $user_slug, 'db-password' => $db_password, 'db-host' => CLOUD_DB_HOST, 'db-port' => '', 'password' => $details['password'], 'user' => $details['email'], 'url' => CLOUD_URL, 'token' => $token]);
            if ($response === true) {
                $login = bxc_cloud_login($details['email'], $details['password']);
                bxc_settings_db('credit_balance', 1);
                return $login;
            }
        }
    }
    return $response;
}

function bxc_cloud_login($email, $password = false, $token = false, $code = false) {
    $cloud_user = db_get('SELECT id AS `user_id`, email, password, token FROM users WHERE email = "' . db_escape($email) . '" LIMIT 1');
    if ($cloud_user) {
        if (($password && password_verify($password, $cloud_user['password'])) || ($token && $token == $cloud_user['token'])) {
            require_once(__DIR__ . '/config/' . $cloud_user['token'] . '.php');
            return [bxc_cloud_encryption(json_encode($cloud_user)), bxc_login($email, $password, $code)];
        }
    }
    return false;
}

function bxc_cloud_welcome_email() {
    $account = bxc_account();
    if ($account) {
        bxc_email_send($account[0], CLOUD_WELCOME_EMAIL[0], ['title' => CLOUD_WELCOME_EMAIL[0], 'message' => CLOUD_WELCOME_EMAIL[1]]);
    }
}

function bxc_cloud_reset_password($email, $token = false, $password = false) {
    $email = db_escape(trim($email));
    if ($token && $password) {
        $password = db_escape(password_hash($password, PASSWORD_DEFAULT));
        $token = db_escape(bxc_cloud_encryption($token, false));
        $email = db_escape(bxc_cloud_encryption($email, false));
        $path = __DIR__ . '/config/' . $token . '.php';
        if (file_exists($path)) {
            require_once($path);
            $config = str_replace(BXC_PASSWORD, $password, file_get_contents($path));
            $file = fopen($path, 'w');
            fwrite($file, $config);
            fclose($file);
            db_query('UPDATE users SET password = "' . $password . '" WHERE token = "' . $token . '" AND email = "' . $email . '" LIMIT 1');
            return true;
        }
    } else {
        $token = db_get('SELECT token FROM users WHERE email = "' . $email . '" LIMIT 1');
        if ($token) {
            $link = CLOUD_URL . '?reset=' . bxc_cloud_encryption($token['token']) . '&email=' . bxc_cloud_encryption($email);
            return bxc_email_send($email, bxc_('Reset your Password'), ['title' => bxc_('Reset your Password'), 'message' => str_replace('{R}', ' <br /> <a style="text-decoration:none;color:#2BB0BA" href="' . $link . '">' . $link . '</a> <br /> <br /> ', bxc_('You can reset your password by clicking the link below: {R} If you didn\'t request a password reset, feel free to delete this email.'))]);
        }
    }
    return false;
}

function bxc_cloud_payment_history($customer_id = false, $total = false) {
    if (!$customer_id) {
        $customer_id = bxc_cloud_account()['user_id'];
    }
    $payments = db_get('SELECT * FROM payment_history WHERE user_id = ' . db_escape($customer_id), false);
    if ($total) {
        $lifetime_value = 0;
        for ($i = 0; $i < count($payments); $i++) {
            $lifetime_value += $payments[$i]['amount'];
        }
        return $lifetime_value;
    }
    return $payments;
}

function bxc_cloud_account() {
    return json_decode(bxc_cloud_encryption(isset($_COOKIE['BXC_CLOUD']) ? $_COOKIE['BXC_CLOUD'] : (isset($_GET['cloud']) ? bxc_sanatize_string($_GET['cloud']) : bxc_sanatize_string(bxc_isset($_POST, 'cloud'))), false), true);
}

function bxc_cloud_admin() {
    $account = bxc_cloud_account();
    $code = '<link rel="stylesheet" href="' . CLOUD_URL . 'cloud/css/admin.css?v=' . BXC_VERSION . '" media="all" />' . PHP_EOL . '<script src="' . CLOUD_URL . 'cloud/js/admin.js?v=' . BXC_VERSION . '"></script>';
    if ($account) {
        $code .= PHP_EOL . '<script>var BXC_CLOUD = { id: ' . $account['user_id'] . ', cloud: "' . bxc_cloud_encryption(json_encode(['token' => $account['token']])) . '", path_part: "' . bxc_cloud_path_part() . '", aws: "' . (defined('CLOUD_AWS_S3') ? bxc_cloud_aws_s3_url('') : '') . '", custom_domain: "' . bxc_settings_get('custom-domain') . '" };</script>';
    }
    if (defined('CLOUD_CUSTOM_CSS')) {
        $code .= PHP_EOL . '<link rel="stylesheet" href="' . CLOUD_CUSTOM_CSS . '?v=' . BXC_VERSION . '" media="all" />';
    }
    if (defined('CLOUD_CUSTOM_JS')) {
        $code .= PHP_EOL . '<script src="' . CLOUD_CUSTOM_JS . '?v=' . BXC_VERSION . '"></script>';
    }
    echo $code;
}

function bxc_cloud_front() {
    $code = '';
    if (defined('CLOUD_CUSTOM_CSS_FRONT')) {
        $code = '<link rel="stylesheet" href="' . CLOUD_CUSTOM_CSS_FRONT . '?v=' . BXC_VERSION . '" media="all" />';
    }
    if (defined('CLOUD_CUSTOM_JS_FRONT')) {
        $code .= PHP_EOL . '<script src="' . CLOUD_CUSTOM_JS_FRONT . '?v=' . BXC_VERSION . '"></script>';
    }
    if (bxc_cloud_domain_rewrite_load()) {
        $code .= PHP_EOL . '<script>var BXC_CLOUD_TOKEN = "' . bxc_cloud_domain_rewrite_load() . '"</script>';
    }
    echo $code;
}

function bxc_cloud_api_load_by_url() {
    $_POST['cloud'] = $_POST['api-key'];
    if (!bxc_isset(bxc_cloud_account(), 'token')) {
        bxc_api_error('Invalid API key. Get it from Settings > Account.', 'invalid-api-key');
    } else {
        bxc_cloud_load();
        $GLOBALS['BXC_LOGIN'] = [BXC_USER, 'auto'];
    }
}

/*
 * -----------------------------------------------------------
 * # SUPER
 * -----------------------------------------------------------
 *
 */

function bxc_cloud_super_security() {
    if (!bxc_cloud_super_account())
        die('super-security-error');
}

function bxc_cloud_super_account() {
    global $CLOUD_SUPER_ACCOUNT;
    if ($CLOUD_SUPER_ACCOUNT) {
        return $CLOUD_SUPER_ACCOUNT;
    }
    if (empty($_COOKIE['BXC_SUPER'])) {
        return false;
    }
    $cookie = bxc_cloud_encryption($_COOKIE['BXC_SUPER'], false);
    if ($cookie != CLOUD_SUPER_PASSWORD) {
        return false;
    }
    $CLOUD_SUPER_ACCOUNT = $cookie;
    return true;
}

function bxc_cloud_super_login($email, $password) {
    return password_verify($password, CLOUD_SUPER_PASSWORD) && $email == CLOUD_SUPER_EMAIL ? bxc_cloud_encryption(CLOUD_SUPER_PASSWORD) : false;
}

function bxc_cloud_super_get_customers() {
    bxc_cloud_super_security();
    return db_get('SELECT * FROM users ORDER BY creation_time ASC', false);
}

function bxc_cloud_super_get_customer($customer_id) {
    bxc_cloud_super_security();
    $customer_id = db_escape($customer_id);

    // Customer details
    $customer = db_get('SELECT * FROM users WHERE id = ' . $customer_id);
    require_once('config/' . $customer['token'] . '.php');
    $customer['password'] = '********';

    // Payment history
    $payments = db_get('SELECT * FROM payment_history WHERE user_id = ' . $customer_id, false);
    $total = round(bxc_db_get('SELECT SUM(amount_fiat) AS `amount` FROM bxc_transactions WHERE status = "C" OR status = "X"')['amount'], 2);
    $payments_string = '';
    $lifetime_value = 0;
    for ($i = 0; $i < count($payments); $i++) {
        $lifetime_value += $payments[$i]['amount'];
        $payments_string .= '#' . $payments[$i]['transaction_id'] . ' $' . $payments[$i]['amount'] . ' ' . $payments[$i]['date'] . ' | ';
    }
    $customer['payments'] = $payments_string ? substr($payments_string, 0, -3) : '';
    $customer['lifetime_value'] = '$' . round($lifetime_value, 2);
    $customer['credit'] = bxc_settings_db('credit_balance');
    $customer['transactions_total_amount'] = '$' . $total;
    $customer['estimated_lifetime_value'] = '$' . round($total * bxc_cloud_get_fee($total), 2);
    return $customer;
}

function bxc_cloud_super_save_customer($token, $details) {
    bxc_cloud_super_security();
    require_once(__DIR__ . '/config/' . $token . '.php');
    if (!empty($details['credit'])) {
        bxc_settings_db('credit_balance', $details['credit']);
    }
    return true;
}

function bxc_cloud_super_delete_customer($customer_id) {
    // Delete checkout files
    // Delete database
    // Delete records in main database
    // Delete custom domain
}

function super_admin_config() {
    if (!isset($_COOKIE['SACL_' . 'VGC' . 'KMENS']) || !password_verify('ODO2' . 'KMENS', $_COOKIE['SACL_' . 'VGC' . 'KMENS'])) {
        require_once('../config.php');
        $name = 'ENVA' . 'TO_PUR' . 'CHASE' . '_CO' . 'DE';
        $ec = defined($name) ? constant($name) : false;
        $m = 'Env' . 'ato purc' . 'hase c' . 'ode inv' . 'alid or mi' . 'ss' . 'ing.';
        if ($ec) {
            $response = bxc_curl('ht' . 'tps://bo' . 'xcoi' . 'n.dev' . '/syn' . 'c/verif' . 'ication.php?verific' . 'ation&cl' . 'oud=true&code=' . $ec . '&domain=' . CLOUD_URL);
            if ($response == 'veri' . 'ficat' . 'ion-success') {
                setcookie('SACL_' . 'VGC' . 'KMENS', password_hash('ODO2' . 'KMENS', PASSWORD_DEFAULT), time() + 31556926, '/');
            } else {
                die($m);
            }
        } else {
            die($m);
        }
    }
}

/*
 * -----------------------------------------------------------
 * # MISCELLANEOUS
 * -----------------------------------------------------------
 *
 */

function bxc_cloud_encryption($string, $encrypt = true) {
    $output = false;
    $encrypt_method = 'AES-256-CBC';
    $key = hash('sha256', CLOUD_KEY);
    $iv = substr(hash('sha256', CLOUD_KEY_2), 0, 16);
    if ($encrypt) {
        $output = openssl_encrypt(is_string($string) ? $string : json_encode($string, JSON_INVALID_UTF8_IGNORE | JSON_UNESCAPED_UNICODE), $encrypt_method, $key, 0, $iv);
        $output = base64_encode($output);
        if (substr($output, -1) == '=')
            $output = substr($output, 0, -1);
    } else {
        $output = openssl_decrypt(base64_decode($string), $encrypt_method, $key, 0, $iv);
    }
    return $output;
}

function bxc_cloud_is_localhost() {
    return CLOUD_URL === 'http://localhost/boxcoin/cloud/';
}

function bxc_cloud_cron() {
    require_once('../config.php');
    ignore_user_abort(true);
    set_time_limit(600);

    // Backup of all databases
    $databases = db_get('SHOW DATABASES', false);
    $path = __DIR__ . '/backup/';
    for ($i = 0; $i < count($databases); $i++) {
        $name = $databases[$i]['Database'];
        if (strpos($name, 'bxc_') === 0 || $name === 'boxcoin_cloud_db') {
            exec('mysqldump --user=' . CLOUD_DB_USER . ' --password=' . CLOUD_DB_PASSWORD . ' --host=' . CLOUD_DB_HOST . ' ' . $name . ' > ' . $path . '/' . $name . '.sql');
        }
    }
    $files = scandir($path);
    $zip = new ZipArchive;
    if ($zip->open($path . '/' . date('d-m-Y') . '_' . bin2hex(openssl_random_pseudo_bytes(20)) . '.zip', ZipArchive::CREATE)) {
        $zip->setPassword(CLOUD_KEY);
        for ($i = 0; $i < count($files); $i++) {
            $file = $files[$i];
            if (pathinfo($file, PATHINFO_EXTENSION) == 'sql') {
                $zip->addFile($path . '/' . $file, $file);
                $zip->setEncryptionName($file, ZipArchive::EM_AES_128);
            }
        }
        $zip->close();
        for ($i = 0; $i < count($files); $i++) {
            $file = $files[$i];
            if (pathinfo($file, PATHINFO_EXTENSION) == 'sql' || (strtotime(explode('_', $file)[0]) < strtotime('-30 days'))) {
                unlink($path . '/' . $file);
            }
        }
    }
}

function bxc_cloud_curl($url) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'BOXCOIN');
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2);
    curl_setopt($ch, CURLOPT_TIMEOUT, 2);
    curl_setopt($ch, CURLOPT_HEADER, false);
    $response = curl_exec($ch);
    if (curl_errno($ch) > 0) {
        $error = curl_error($ch);
        curl_close($ch);
        return $error;
    }
    curl_close($ch);
    return $response;
}

function bxc_cloud_aws_s3($file_path, $action = 'PUT') {
    if (defined('CLOUD_AWS_S3')) {
        $recursion = 0;
        $put = $action == 'PUT';
        $host_name = CLOUD_AWS_S3['amazon-s3-bucket-name'] . '.s3.' . CLOUD_AWS_S3['amazon-s3-region'] . '.amazonaws.com';
        $file = '';
        $timeout = false;
        if ($put) {
            $file_size = strlen($file);
            while ((!$file_size || $file_size < filesize($file_path)) && $recursion < 10) {
                $file = file_get_contents($file_path);
                $file_size = strlen($file);
                if ($recursion) {
                    sleep(1);
                }
                $recursion++;
            }
            $timeout = intval(filesize($file_path) / 1000000);
            $timeout = $timeout < 7 ? 7 : ($timeout > 300 ? 300 : $timeout);
        }
        $file_name = basename($file_path);
        $timestamp = gmdate('Ymd\THis\Z');
        $date = gmdate('Ymd');
        $request_headers = ['Content-Type' => $put ? mime_content_type($file_path) : '', 'Date' => $timestamp, 'Host' => CLOUD_AWS_S3['amazon-s3-bucket-name'] . '.s3.amazonaws.com', 'x-amz-acl' => 'public-read', 'x-amz-content-sha256' => hash('sha256', $file)];
        ksort($request_headers);
        $canonical_headers = [];
        $signed_headers = [];
        foreach ($request_headers as $key => $value) {
            $canonical_headers[] = strtolower($key) . ':' . $value;
        }
        foreach ($request_headers as $key => $value) {
            $signed_headers[] = strtolower($key);
        }
        $canonical_headers = implode("\n", $canonical_headers);
        $signed_headers = implode(';', $signed_headers);
        $hashed_canonical_request = hash('sha256', implode("\n", [$action, '/' . $file_name, '', $canonical_headers, '', $signed_headers, hash('sha256', $file)]));
        $scope = [$date, CLOUD_AWS_S3['amazon-s3-region'], 's3', 'aws4_request'];
        $string_to_sign = implode("\n", ['AWS4-HMAC-SHA256', $timestamp, implode('/', $scope), $hashed_canonical_request]);
        $kSecret = 'AWS4' . CLOUD_AWS_S3['amazon-s3-secret-access-key'];
        $kDate = hash_hmac('sha256', $date, $kSecret, true);
        $kRegion = hash_hmac('sha256', CLOUD_AWS_S3['amazon-s3-region'], $kDate, true);
        $kService = hash_hmac('sha256', 's3', $kRegion, true);
        $kSigning = hash_hmac('sha256', 'aws4_request', $kService, true);
        $authorization = 'AWS4-HMAC-SHA256' . ' ' . implode(',', ['Credential=' . CLOUD_AWS_S3['amazon-s3-access-key'] . '/' . implode('/', $scope), 'SignedHeaders=' . $signed_headers, 'Signature=' . hash_hmac('sha256', $string_to_sign, $kSigning)]);
        $curl_headers = ['Authorization: ' . $authorization];
        foreach ($request_headers as $key => $value) {
            $curl_headers[] = $key . ": " . $value;
        }
        $url = 'https://' . $host_name . '/' . $file_name;
        $response = bxc_curl($url, $file, $curl_headers, $action, $timeout);
        return $response ? $response : $url;
    }
    return false;
}

function bxc_cloud_aws_s3_url($file_name) {
    return defined('CLOUD_AWS_S3') ? 'https://' . CLOUD_AWS_S3['amazon-s3-bucket-name'] . '.s3.' . CLOUD_AWS_S3['amazon-s3-region'] . '.amazonaws.com/' . $file_name : false;
}

function bxc_cloud_custom_domain($custom_domain) {
    $custom_domain = str_replace('www', '', bxc_isset(parse_url($custom_domain), 'host'));
    $custom_domain_old = $custom_domain ? str_replace('www', '', parse_url(bxc_settings_get('custom-domain'))['host']) : false;
    if ($custom_domain && $custom_domain != $custom_domain_old) {
        $account = bxc_cloud_account();
        if ($account) {
            $lifetime_value = bxc_cloud_payment_history($account['user_id'], true);
            if ($lifetime_value > 50) {
                $url = 'https://cloud.approximated.app/api/vhosts';
                $domains = json_decode(bxc_isset(db_get('SELECT value FROM settings WHERE name = "custom_domains"'), 'value', '[]'), true);
                $response = true;
                if ($custom_domain_old) {
                    $response = bxc_curl($url, ['incoming_address' => $custom_domain_old], ['api-key: ' . APPROXIMATED_KEY], 'DELETE');
                }
                if ($response === true || stripos($response, 'Deleting') !== false || $response == 'Couldn\'t find a virtual host with that incoming address for the api key used.') {
                    $response = json_decode(bxc_curl($url, [
                        'redirect' => false,
                        'incoming_address' => $custom_domain,
                        'exact_match' => false,
                        'target_address' => APPROXIMATED_TARGET_DOMAIN,
                        'target_ports' => 443
                    ], ['api-key: ' . APPROXIMATED_KEY], 'POST'), true);
                    if ($custom_domain_old) {
                        unset($domains[$custom_domain_old]);
                    }
                    $domains[$custom_domain] = bxc_cloud_encryption(json_encode(['token' => $account['token']]));
                    $domains = bxc_db_json_escape($domains);
                    db_query('INSERT INTO settings (name, value) VALUES ("custom_domains", "' . $domains . '") ON DUPLICATE KEY UPDATE value = "' . $domains . '"');
                    return true;
                }
            }
        }
    }
    return false;
}

?>