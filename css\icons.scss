
/*
* 
* ==========================================================
* ICONS.SCSS
* ==========================================================
*
* Icons file shared by admin and client
*
*/

@font-face {
    font-family: 'Boxcoin Icons';
    src: url('../media/fonts/icons.ttf?v1') format('truetype'), url('../media/fonts/icons.woff?v1') format('woff'), url('../media/fonts/icons.svg?v1#Boxcoin-Icons') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

[class^="bxc-icon-"]:before,
[class*=" bxc-icon-"]:before {
    font-family: "Boxcoin Icons" !important;
    font-style: normal !important;
    font-weight: normal !important;
    font-variant: normal !important;
    text-transform: none !important;
    speak: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.bxc-icon-copy:before {
    content: "\e90d";
}

.bxc-icon-back:before {
    content: "\e90c";
}

.bxc-icon-search:before {
    content: "\e90b";
}

.bxc-icon-shuffle:before {
    content: "\e904";
}

.bxc-icon-automation:before {
    content: "\e905";
}

.bxc-icon-bar-chart:before {
    content: "\e906";
}

.bxc-icon-delete:before {
    content: "\e907";
}

.bxc-icon-menu:before {
    content: "\e908";
}

.bxc-icon-plus-2:before {
    content: "\e909";
}

.bxc-icon-settings:before {
    content: "\e90a";
}

.bxc-icon-close:before {
    content: "\e903";
}

.bxc-icon-help:before {
    content: "\e902";
}

.bxc-icon-check:before {
    content: "\e901";
}

.bxc-icon-loader:before {
    content: "\e900";
}

.bxc-icon-download:before {
    content: "\66";
}

.bxc-icon-clip:before {
    content: "\65";
}

.bxc-icon-filters:before {
    content: "\e90e";
}

.bxc-icon-arrow-down:before {
    content: "\61";
}

.bxc-icon-user:before {
    content: "\6e";
}

.bxc-icon-arrow-right:before {
    content: "\ea3c";
}

.bxc-icon-arrow-left:before {
    content: "\ea40";
}

.bxc-icon-clock:before {
    content: "\46";
}

.bxc-icon-calendar:before {
    content: "\62";
}

.bxc-icon-image:before {
  content: "\e90f";
}