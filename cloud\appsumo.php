<?php

/*
 *
 * ===================================================================
 * APPSUMO
 * ===================================================================
 *
 */

$is_login = isset($_COOKIE['BXC_CLOUD']) && isset($_COOKIE['BXC_LOGIN']);
$message = '';
if ($is_login && isset($_POST['appsumo-code-1'])) {
    require('../functions.php');
    require('functions.php');
    $appsumo_code = trim($_POST['appsumo-code-1']);
    $codes = json_decode(file_get_contents('appsumo_2ui14f6d63c0abd86i798fb98ea81cd6c86e306.json'), true);
    if (in_array($appsumo_code, $codes)) {
        $used_codes = json_decode(db_get('SELECT value FROM settings WHERE name = "appsumo_used_codes" LIMIT 1')['value'], true);
        if (in_array($appsumo_code, $used_codes)) {
            $message = 'Sorry, this code has already been used.';
        } else {
            // $49 - $250 | $98 - $650 | $147 - $1000
            $account = bxc_cloud_account();
            if ($account['user_id']) {
                array_push($used_codes, $appsumo_code);
                $credit = 250;
                for ($i = 2; $i < 4; $i++) {
                	if (!empty($_POST['appsumo-code-' . $i])) {
                        $appsumo_code_2 = trim($_POST['appsumo-code-' . $i]);
                        if (in_array($appsumo_code_2, $codes) && !in_array($appsumo_code_2, $used_codes)) {
                            $credit += ($i === 2 ? 400 : 350);
                            array_push($used_codes, $appsumo_code_2);
                            $appsumo_code .= ' | ' . $appsumo_code_2;
                        }
                    }
                }
                bxc_cloud_load();
                db_query('UPDATE settings SET value = "' . str_replace('\"', '"', db_escape(json_encode($used_codes))) . '" WHERE name = "appsumo_used_codes" LIMIT 1');
                db_query('INSERT INTO payment_history (user_id, amount, date, transaction_id, invoice) VALUES ("' . $account['user_id'] . '", "' . $credit . '", NOW(), "' . $appsumo_code . '", false)');
                bxc_db_query('UPDATE bxc_settings SET value = value + ' . $credit . ' WHERE name = "credit_balance"');
                $message = true;
            } else {
                $message = 'Account login error. Try to login again or use another browser.';
            }
        }
    } else {
        $message = 'Invalid code. The AppSumo code you entered is invalid.';
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no" />
    <title>
        AppSumo Code Redemption | Boxcoin
    </title>
    <link rel="stylesheet" href="../css/admin.css" media="all" />
    <link rel="shortcut icon" type="image/svg" href="../media/icon.svg" />
    <style>
    .bxc-input {
        align-items: center;
    }

    .bxc-input input {
        margin-left: 15px !important;
    }

    .bxc-top > img {
        width: 250px;
        margin: 0 0 60px 0;
    }

    .bxc-top + .bxc-bottom {
        padding-top: 0;
    }

    </style>
</head>
<body>
    <?php if ($message === true) die('<script>document.location = "https://cloud.boxcoin.dev/#account";</script>'); ?>
    <div class="bxc-main bxc-cloud-registration bxc-box">
        <form method="post">
            <?php echo '<div class="bxc-info">' . $message . '</div>' ?>
            <div class="bxc-top">
                <img src="../media/logo.svg" />
                <div class="bxc-title">
                    <?php echo $is_login ? 'AppSumo Code Redemption' : 'Register or login to continue' ?>
                </div>
                <div class="bxc-text">
                    <?php echo $is_login ? 'Enter the AppSumo code in the field below to redeem your balance credit.' : 'Please register a new Boxcoin account or log in before redeeming your AppSumo code.' ?>
                </div>
            </div>
            <?php if ($is_login) echo '<div class="bxc-input"><span>AppSumo Code 1</span><input name="appsumo-code-1" type="text" required /></div><div class="bxc-input"><span>AppSumo Code 2</span><input name="appsumo-code-2" type="text" /></div><div class="bxc-input"><span>AppSumo Code 3</span><input name="appsumo-code-3" type="text" /></div>' ?>
            <div class="bxc-bottom">
                <?php echo $is_login ? '<input type="submit" class="bxc-btn" value="Activate code now" />' : '<a href="https://cloud.boxcoin.dev/?register" class="bxc-btn">Register a new account</a><a href="https://cloud.boxcoin.dev/" class="bxc-btn">Log in</a>'; ?>
            </div>
        </form>
    </div>
</body>
</html>