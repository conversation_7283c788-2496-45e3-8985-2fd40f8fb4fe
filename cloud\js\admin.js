
/*
* 
* ===================================================================
* CLOUD FILE BOXCOIN ADMIN AREA
* ===================================================================
*
*/

var _ = window._query;
var URL = document.location.href;


document.addEventListener('DOMContentLoaded', () => {
    let box_registration = _('.bxc-cloud-registration');
    let box_account = _('[data-area="account"]');
    let box_login = _('.bxc-cloud-login');
    let box_reset_password = _('.bxc-cloud-reset-password');
    let messages = {
        password_length: bxc_('The password must be at least 8 characters long.'),
        password_match: bxc_('The passwords do not match.'),
        email: bxc_('The email address is not valid.')
    };
    let reset_password = URL.includes('reset=');
    let settings = _('[data-area="settings"]');

    if (URL.includes('register')) {
        box_login.removeClass('active');
        box_registration.addClass('active');
        document.title = document.title.replace('Login', 'Sign up');
    }

    // Registration and login area
    if (box_registration.e.length) {
        box_registration.on('click', '#bxc-submit-registration', function (e) {
            if (loading(this)) return;
            let details = {};
            let errors = false;
            let errors_area = box_registration.find('.bxc-info');
            errors_area.html('');
            box_registration.find('[id].bxc-input').e.forEach(e => {
                let input = _(e).find('input');
                if (!input.val().trim()) {
                    input.addClass('bxc-error');
                    errors = true;
                } else input.removeClass('bxc-error');
                details[_(e).attr('id')] = input.val().trim();
            });
            if (errors) {
                errors_area.html(bxc_('All fields are required.'));
            } else if (details['password'].length < 8) {
                errors_area.html(messages.password_length);
                errors = true;
            } else if (details['password'] != details['password-check']) {
                errors_area.html(messages.password_match);
                errors = true;
            } else if (!details['email'].includes('@') || !details['email'].includes('.')) {
                errors_area.html(messages.email);
                errors = true;
            } else {
                setLogin('', '');
                if (URL.includes('appsumo')) {
                    details['appsumo'] = getUrlParameter('appsumo');
                }
                if (URL.includes('ref=')) {
                    BOXCoin.cookie('bxc-referral', getUrlParameter('ref'), 60);
                }
                BOXCoin.ajax('cloud', { action: 'registration', arguments: details }, (response) => {
                    if (response == 'duplicate-email') {
                        errors_area.html(bxc_('This email is already in use. Please use another email.'));
                    } else {
                        setLogin(response[0], response[1]);
                        BOXCoin.ajax('cloud', { action: 'welcome-email' });
                        setTimeout(() => { location.href = BXC_URL }, 300);
                    }
                    loading(this, false);
                });
            }
            if (errors) {
                loading(this, false);
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
            e.preventDefault();
            return false;
        });

        box_login.on('click', '#bxc-submit-cloud-login', function (e) {
            let email = box_login.find('#email');
            let password = box_login.find('#password');
            let twofa = box_login.find('#two-fa');
            let errors_area = box_login.find('.bxc-info');
            if (!email || !password || loading(this)) {
                return;
            }
            BOXCoin.ajax('cloud', { action: 'login', arguments: { email: email.find('input').val(), password: password.find('input').val(), code: twofa.e.length ? twofa.find('input').val() : false } }, (response) => {
                let error;
                if (response === false) {
                    error = 'Invalid email or password.';
                } else {
                    setLogin(response[0], response[1]);
                    if (response[1] === '2fa') {
                        if (twofa.e.length) {
                            error = 'Invalid code.';
                        } else {
                            password.insert('<div id="two-fa" class="bxc-input"><span>' + bxc_('Verification code') + '</span><input type="password" required /></div>', false);
                        }
                    } else {
                        location.href = BXC_URL;
                    }
                }
                if (error) {
                    errors_area.html(bxc_(error));
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                }
                loading(this, false);
            });
            e.preventDefault();
            return false;
        });

        box_login.on('click', '#bxc-registration-box', function () {
            box_login.removeClass('active');
            box_registration.addClass('active');
            document.title = document.title.replace('Login', 'Sign up');
        });

        box_registration.on('click', '#bxc-login-box', function () {
            box_registration.removeClass('active');
            box_login.addClass('active');
            document.title = document.title.replace('Sign up', 'Login');
        });

        box_reset_password.on('click', '#bxc-reset-password', function () {
            let email = box_reset_password.find('#reset-password-email').val().trim();
            if (email && email.includes('@') && email.includes('.')) {
                BOXCoin.ajax('cloud', { action: 'reset-password', arguments: { email: email } });
                box_reset_password.html(`<div><div class="bxc-title">${bxc_('Check your email')}</div><div class="bxc-text">${bxc_('If an account linked to the email provided exists you will receive an email with a link to reset your password.')}</div></div>`);
            }
        });

        box_reset_password.on('click', '#bxc-cancel-reset-password', function () {
            box_reset_password.removeClass('active');
            box_login.addClass('active');
        });

        box_login.on('click', '#bxc-forgot-password', function () {
            box_registration.removeClass('active');
            box_login.removeClass('active');
            box_reset_password.addClass('active');
        });

        if (reset_password) {
            let box_reset_password_2 = _('.bxc-reset-password-2');
            let info = box_reset_password_2.find('.bxc-info');
            box_reset_password_2.on('click', '#bxc-reset-password-2', function () {
                let password = box_reset_password_2.find('#reset-password-1').val();
                info.html('');
                if (!password) return;
                if (password != box_reset_password_2.find('#reset-password-2').val()) {
                    return info.html(messages.password_match);
                }
                if (password.length < 8) {
                    return info.html(messages.password_length);
                }
                if (loading(this)) return;
                BOXCoin.ajax('cloud', { action: 'reset-password', arguments: { email: BOXCoin.getURL('email'), token: BOXCoin.getURL('reset'), password: password } }, () => {
                    window.history.replaceState({}, document.title, location.href.substring(0, location.href.indexOf('?reset=')));
                    box_login.addClass('active');
                    box_reset_password_2.removeClass('active');
                    loading(this, false);
                });
            });
        }

        document.addEventListener('keydown', function (e) {
            if (e.which == 13) {
                _('#bxc-submit-login').e[0].click();
            }
        });
    }

    // Account and admin area
    if (box_account.e.length) {
        if (reset_password) _('#bxc-logout').e[0].click();
        box_account.find('#account-api-key').val(BXC_CLOUD.cloud);
        box_account.on('click', '#reset-password', function () {
            BOXCoin.ajax('cloud', { action: 'reset-password', arguments: { email: box_account.find('#account-email').val() } });
            BXCAdmin.card('Check your inbox, you will receive an email with a link to reset your password.');
        });
    }

    // Settings area
    if (settings.e.length) {
        let items = settings.find('#btc-wallet-key,#eth-wallet-key,#btc-node-transfer,#btc-node-transfer-address,#eth-node-transfer,#eth-node-transfer-address,#eth-node-conversion,#eth-node-conversion-currency').addClass('bxc-cloud-disabled');
        let custom_domain_check = false;
        for (var i = 0; i < items.e.length; i++) {
            _(items.e[i]).find('input,select').attr('disabled', true);
        }

        settings.on('mouseover', '#custom-domain', function () {
            if (!custom_domain_check) {
                BOXCoin.ajax('cloud', { action: 'customer-lifetime-value' }, (response) => {
                    custom_domain_check = true;
                    if (response < 50) {
                        _(this).addClass('bxc-cloud-disabled').addClass('bxc-credit-disabled');
                    }
                });
            }
        });
    }
});

/*
* ----------------------------------------------------------
* Functions
* ----------------------------------------------------------
*/

function loading(element, action = -1) {
    return BOXCoin.loading(element, action);
}

function activate(element, activate = true) {
    return BOXCoin.activate(element, activate);
}

function bxc_(text) {
    return BXC_TRANSLATIONS && text in BXC_TRANSLATIONS ? BXC_TRANSLATIONS[text] : text;
}

function setLogin(cloud, bxc) {
    BOXCoin.cookie('BXC_CLOUD', cloud, 365, 'set');
    BOXCoin.cookie('BXC_LOGIN', bxc[0], 365, 'set');
}