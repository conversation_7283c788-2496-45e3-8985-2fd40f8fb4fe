/*

===================================================================
CLOUD ADMIN CSS FILE
===================================================================

*/

$color-main: #2bb0ba;
$color-main-hover: #2bcbd7;

.bxc-cloud-box {
    margin: 30px auto !important;

    .bxc-bottom {
        justify-content: flex-start;
        display: flex;
        align-items: center;
        padding-top: 50px;

        div + div {
            margin-left: 15px;
        }

        .bxc-text {
            font-size: 15px;
            line-height: 25px;
        }
    }

    #bxc-forgot-password {
        margin: 10px 0 0 0 !important;
        text-align: right;
        font-size: 13px;
    }

    #bxc-cancel-reset-password, #bxc-forgot-password {
        cursor: pointer;

        &:hover {
            color: #ca3434;
            transition: all 0.4s;
        }
    }
}

#bxc-registration-box, #bxc-login-box {
    color: $color-main;
    cursor: pointer;
    margin-left: 5px !important;
}

.disclaimer {
    text-align: center;
    max-width: 600px;
    display: block;
    margin: 0 auto 30px auto;
    font-size: 13px;
    line-height: 25px;
    font-family: "Boxcoin";

    a {
        color: $color-main;
        text-decoration: none;
    }
}

.bxc-cloud-box:not(.active) {
    display: none !important;
}

.bxc-errors-area {
    color: #ca3434;
    margin: 30px 0 0 0;
    font-size: 15px;
    line-height: 25px;
}

input.bxc-error {
    border: 1px solid #ca3434;
    box-shadow: 0 0 5px rgba(202, 52, 52, .25);
}

#bxc-payment-box {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
    grid-gap: 20px;
    max-width: 600px;
    margin-top: 30px;

    > a {
        color: #000;
        text-decoration: none;
        border: 1px solid #d4d4d4;
        border-radius: 4px;
        padding: 20px;
        font-weight: 500;
        letter-spacing: .3px;
        cursor: pointer;
        text-align: center;
        transition: all .3s;

        &:hover {
            border-color: $color-main;
            color: $color-main;
        }
    }
}

#bxc-table-payments {
    margin-top: 30px;
    max-width: 600px;

    img {
        width: 20px;
        display: block;
        margin: 0 0 0 auto;
        transition: all .3s;

        &:hover {
            opacity: .6;
        }
    }

    th:first-child, td:first-child {
        width: 20px;
        overflow: hidden;
    }

    th:last-child, td:last-child {
        text-align: right;
    }
}

.bxc-credits-box a {
    text-decoration: none;
    font-size: 12px;
    padding-left: 5px;
    color: #969696;

    &:hover {
        color: $color-main;
    }
}

#bxc-checkout-embed-code, #bxc-checkout-payment-link {

    div {
        margin-top: 10px;
        max-width: calc(100% - 80px);
        height: 20px;
        white-space: nowrap;
        overflow-x: scroll;
        padding-right: 65px;

        &::-webkit-scrollbar {
            height: 5px;
        }

        &::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        &::-webkit-scrollbar-thumb {
            background: #ced6db;
            border-radius: 6px;
        }

        &::-webkit-scrollbar-thumb:hover {
            background: #A0A0A0;
        }
    }

    i {
        top: 38px;
    }
}

[data-checkout-id] #bxc-checkout-embed-code, [data-checkout-id] #bxc-checkout-payment-link {
    display: block !important;
}

#reset-password {
    font-size: 14px;
}

#bxc-account-list {
    margin-top: 30px;
    max-width: 600px;

    .bxc-input {
        align-items: center;
        margin-bottom: 15px;

        span {
            max-width: 100px;
            flex-shrink: 0;
        }
    }

    .bxc-input + .bxc-input {
        margin-top: 0;
        border-top: none;
    }
}

#account-api-key {
    cursor: text;
}

#api-keys, #openexchangerates-app-id, #update, #js-admin, #envato-purchase-code, #minify, #url-rewrite, #shop-envato-purchase-code, #api-key, #shop-envato-purchase-code {
    display: none !important;
}

#bxc-super-save-customer {
    margin-top: 30px
}

.bxc-cloud-disabled {
    position: relative;

    input, span, select, .bxc-setting-content p {
        opacity: .5;
    }

    input, select {
        transition: all .5s;
        background-color: #FFF !important;
    }

    &:before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        opacity: 0;
        background-color: rgba(255, 255, 255, 1);
        z-index: 9;
    }

    &:after {
        content: "Only available in PHP and WP versions";
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 0;
        right: 0;
        z-index: 11;
        text-align: center;
        font-weight: 500;
        font-size: 13px;
        letter-spacing: .3px;
        white-space: nowrap;
        opacity: 0;
        color: #2bb0ba;
    }

    &[data-type="checkbox"] {
        &:after {
            top: 10px;
        }

        &:before {
            background-color: rgba(255, 255, 255, 1);
        }
    }

    &:hover {

        &:before, &:after {
            opacity: 1;
        }
    }
}

.bxc-credit-disabled:after {
    content: "To access this feature, a minimum credit purchase of 50 USD is required.";
}

/*
     
# RESPONSIVE
==========================================================

*/

@media (max-width: 428px) {
    .bxc-cloud-box {
        border: none;

        .bxc-bottom, .bxc-bottom > div {
            display: block;
            text-align: center;
            margin-bottom: 5px;

            &.bxc-btn {
                margin-bottom: 15px;
            }
        }
    }
}
