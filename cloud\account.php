<div data-area="account">
    <div class="bxc-credits-box">
        <div class="bxc-title">
            USD <?php echo round(bxc_settings_db('credit_balance'), 6) ?>
        </div>
        <div class="bxc-text">
            <?php bxc_e('Credit balance') ?>
            <a href="<?php CLOUD_DOCS ?>#credit-balance" target="_blank">
                <i class="bxc-icon-help"></i>
            </a>
        </div>
    </div>
    <hr />
    <div class="bxc-add-credits-box">
        <div class="bxc-title">
            <?php bxc_e('Add credit to your account') ?>
        </div>
        <div id="bxc-payment-box">
            <?php
            $code = '';
            $prices = [10, 20, 50, 100];
            $external_reference = bxc_cloud_encryption(bxc_cloud_account()['user_id']);
            $base_url = bxc_cloud_is_localhost() ? 'http://localhost/boxcoin/boxcoin' : (CLOUD_URL . 'payment');
            for ($i = 0; $i < count($prices); $i++) {
                $code .= '<a href="' . $base_url . '/pay.php?checkout_id=custom-cloud-' . $i . '&price=' . $prices[$i] . '&currency=usd&redirect=' . urlencode(BXC_URL . '#account') . '&external_reference=' . $external_reference . '" target="_blank">$' . $prices[$i] . '</a>';
            }
            echo $code;
            ?>
        </div>
    </div>
    <hr />
    <div class="bxc-payment-history-box">
        <div class="bxc-title">
            <?php bxc_e('Payment history') ?>
        </div>
        <?php
        $history = bxc_cloud_payment_history();
        $code = '';
        for ($i = 0; $i < count($history); $i++) {
            $code .= '<tr><td>' . $history[$i]['transaction_id'] . '</td><td>' . $history[$i]['date'] . '</td><td>USD ' . $history[$i]['amount'] . '</td><td>' . ($history[$i]['invoice'] ? '<a href="' . $history[$i]['invoice'] . '"><img src="media/pdf.svg" /></a>' : '') . '</td></tr>';
        }
        if ($code) {
            echo '<table id="bxc-table-payments" class="bxc-table"><thead><tr><th data-field="date">ID</th><th data-field="date">' . bxc_('Date') . '</th><th data-field="amount">' . bxc_('Amount'). '</th><th data-field="invoice">' . bxc_('Invoice'). '</th></tr></thead><tbody>' . $code . '</tbody></table>';
        } else {
            echo '<p class="bxc-not-found">' . bxc_('There\'s nothing here yet.') . '</p>';
        }
        ?>
    </div>
    <hr />
    <div class="bxc-account-box">
        <div class="bxc-title">
            <?php bxc_e('Account') ?>
        </div>
        <div id="bxc-account-list">
            <div class="bxc-input">
                <span>
                    <?php bxc_e('Email') ?>
                </span>
                <input id="account-email" value="<?php echo bxc_cloud_account()['email'] ?>" type="text" disabled />
            </div>
            <div class="bxc-input">
                <span>
                    API key
                </span>
                <input id="account-api-key" type="text" disabled />
            </div>
        </div>
        <hr />
        <div id="reset-password" class="bxc-underline bxc-link">
            <?php bxc_e('Reset password') ?>
        </div>
    </div>
</div>