"use strict";!function(){function e(e,t=-1){return BOXCoin.loading(e,t)}function t(e,t={},a=!1){return BOXCoin.ajax(e,t,a)}function a(e,t=!0){return BOXCoin.activate(e,t)}function i(e,t=!1){G.card(e,t)}function n(e){return BXC_TRANSLATIONS&&e in BXC_TRANSLATIONS?BXC_TRANSLATIONS[e]:e}function c(e,t=!1){G.error(e,t)}function o(e,t=-1){return e&&U(e).e.length?(e=U(e).e[0],-1===t?U(e).is("checkbox")?e.checked:e.value.trim():(U(e).is("checkbox")?e.checked=0!=t&&t:e.value=t,void(U(e).is("textarea")&&p(e)))):""}function r(e){return U(e).find("input, select, textarea")}function s(){let e=window.location.href;if(e.includes("#")){let t=e.substr(e.indexOf("#"));if(t.includes("?")&&(t=t.substr(0,t.indexOf("?"))),t.length>1){let e=m.find(".bxc-nav "+t);if(e.e.length)return d(e),!0}}return!1}function d(i){if(!U(i).e.length)return;let n=m.find("main > div"),c=i.index();switch(k=U(n.e[c]),C=i.attr("id"),m.removeClass("bxc-area-transactions bxc-area-checkouts bxc-area-balances bxc-area-settings").addClass("bxc-area-"+C),a(n,!1),a(i.siblings(),!1),a(i),a(k),window.scrollTo(0,0),window.location.href.includes(C)||window.history.pushState("","","#"+C),C){case"transactions":e(k)||(R&&(R.destroy(),R=!1),e(k),T=0,_.find("tbody").html(""),E.print(()=>{e(n.e[c],!1),setTimeout(()=>{window.scrollTo(0,0)},100)}));break;case"checkouts":e(k,"check")&&t("get-checkouts",{},t=>{let a="";I=t;for(var i=0;i<t.length;i++)a+=M.row(t[i]);u(m.find("#bxc-table-checkouts tbody"),a),e(n.e[c],!1)});break;case"balances":G.balance(k);break;case"settings":e(k,"check")&&t("get-settings",{},t=>{if(t){if(BXC_ADMIN_SETTINGS.apps.includes("exchange")){let e="custom-token-code",a="",i=0;for(;t[e+(i?"-"+(i+1):"")];)a+=`<option value="${t[e+(i?"-"+(i+1):"")]}">${t["custom-token-name"+(i?"-"+(i+1):"")]}</option>`,i++;a&&k.find("#exchange-default-get select").append(a)}G.repeater.open(t,k);for(var a in t){let e=m.find("#"+a);e&&o(r(e),t[a])}k.find("#btc-wallet-key input, #eth-wallet-key input, #ln-macaroon input").e.forEach(e=>{e.value&&(e.value="********")}),BOXCoin.event("SettingsLoaded",t)}e(n.e[c],!1)});break;case"analytics":R&&(R.destroy(),R=!1),BXCAdminShop.analytics(R)}}function l(e){return(e=e.replace(/_/g," ").replace(/-/g," ")).charAt(0).toUpperCase()+e.slice(1)}function u(e,t,a=!1){if(!t)return!e.html()&&e.html(`<p class="bxc-not-found">${n("There's nothing here yet.")}</p>`);e.find(".bxc-not-found").remove(),e.html((a?e.html():"")+t)}function h(){p(this)}function p(e){e.style.height="auto",e.style.height=e.scrollHeight+"px"}function f(e="",t=!0){return BXC_CLOUD&&BXC_CLOUD.cloud&&!BXC_CLOUD.custom_domain?(e.includes("?")?"&":"?")+"cloud="+BXC_CLOUD.cloud:""}var b,m,x,v,k,C,_,g,y,w,$,B,X=[!1,!1,!1,!1,!1],D={P:"Pending",C:"Completed",R:"Refunded",X:"Underpayment"},T=0,S=!0,N=new Date,I=!1,O=window.innerWidth<769,R=!1,L=!1,A={format:"dd/mm/yyyy",maxDate:new Date,dateDelimiter:" - ",separator:" - ",clearBtn:!0},U=window._query,E={get:function(e,a=!1,i=!1,n=!1,c=!1,o=!1){t("get-transactions",{pagination:T,search:a,status:i,cryptocurrency:n,date_range:c,checkout_id:o},t=>{T++,e(t)})},print:function(e,t=X[0],a=X[1],i=X[2],c=X[3],o=X[4]){this.get(t=>{let a="";S=t.length;for(var i=0;i<S;i++){let e=t[i],o=e.from?`<a href="${G.explorer(e.cryptocurrency,e.from)}" target="_blank" class="bxc-link">${e.from}</a>`:"",r=e.currency.toUpperCase()+" "+e.amount_fiat,s="",d=["","","","","",""],u="",h=[e.amount?e.amount+" "+BOXCoin.baseCode(e.cryptocurrency.toUpperCase()):e.amount_fiat?r:n("Free"),e.amount?r:e.amount_fiat?l(BOXCoin.baseCode(e.cryptocurrency)):""];if(O){d=["ID","Date","From","To","Status","Amount"];for(var c=0;c<d.length;c++)d[c]='<div class="bxc-label">'+n(d[c])+"</div>"}e.vat_details&&(s=JSON.parse(e.vat_details),s=` data-vat="${e.currency} ${s.amount} (${s.percentage}%, ${s.country})"`),BXC_ADMIN_SETTINGS.apps.includes("shop")&&(u=` data-license-key="${e.license_key?e.license_key:""}" data-license-key-status="${e.license_key_status?e.license_key_status:""}" data-customer-id="${e.customer_id?e.customer_id:""}"`),a+=`<tr data-id="${e.id}"${u} data-checkout-id="${e.checkout_id}" data-title="${e.title}" data-currency="${e.currency}" data-cryptocurrency="${e.cryptocurrency}" data-hash="${e.hash}" data-notes="${e.description.join("<br>")}" data-status="${e.status}"${e.billing?' data-invoice="true"':""}${s} data-discount-code="${e.discount_code?e.discount_code:""}" data-amount="${e.amount_fiat?e.amount_fiat:e.amount}" data-type="${e.type?e.type:1}"><td class="bxc-td-id">${d[0]}${e.id}</td><td class="bxc-td-time">${d[1]}<div class="bxc-title">${BOXCoin.beautifyTime(e.creation_time,!0)}</div></td><td class="bxc-td-from">${o?d[2]:""}${o}</td><td class="bxc-td-to">${e.to?d[3]:""}${e.to?`<a href="${G.explorer(e.cryptocurrency,e.to)}" target="_blank" class="bxc-link">${e.to}</a>`:""}</td><td class="bxc-td-status">${d[4]}<span class="bxc-status-${e.status}">${n(D[e.status])}</span></td><td class="bxc-td-amount">${d[5]}<div class="bxc-title"><div>${h[0]}</div><div>${e.cryptocurrency&&h[1].includes(e.cryptocurrency.toUpperCase())?"":h[1]}</div></div></td><td><i class="bxc-transaction-menu-btn bxc-icon-menu"></i></td></tr>`}u(_.find("tbody"),a,!0),e&&e(t)},t,a,i,c,o)},query:function(t=!1){e(_)||(_.find("tbody").html(""),T=0,X[0]=U(m).find("#bxc-search-transactions").val().toLowerCase().trim(),X[1]=U(m).find(".bxc-filter-status li.bxc-active").data("value"),X[2]=U(m).find(".bxc-filter-cryptocurrency li.bxc-active").data("value"),X[3]=!!R&&R.getDates("yyyy-mm-dd"),X[4]=U(m).find(".bxc-filter-checkout li.bxc-active").data("value"),this.print(()=>{t&&e(t,!1),e(_,!1)}))},download:function(e){t("download-transactions",{search:X[0],status:X[1],cryptocurrency:X[2],date_range:X[3],checkout_id:X[4]},t=>{e(t)})}},M={row:function(e){return`<tr data-checkout-id="${e.id}"><td><div class="bxc-title"><span>${e.id}</span><span>${e.title}</span></div></td><td><div class="bxc-text">${e.currency?e.currency:BXC_CURRENCY} ${e.price}</div></td></tr>`},embed:function(e=!1){let t=g?3:2;for(var a=0;a<t;a++){let t=k.find("#bxc-checkout-"+(0==a?"payment-link":1==a?"embed-code":"shortcode")).find("div, i");if(e){let i="";0==a?(i=`${BXC_CLOUD.custom_domain?BXC_CLOUD.custom_domain:BXC_URL}${BXC_CLOUD.cloud?"/checkout/":BXC_ADMIN_SETTINGS.url_rewrite_checkout?BXC_ADMIN_SETTINGS.url_rewrite_checkout:"pay.php?checkout_id="}${e}`,i+=BXC_CLOUD.custom_domain?"":f(i)):i=1==a?`<div data-bxc="${e}"></div>${BXC_CLOUD.cloud?' <script id="bxc-cloud" src="'+BXC_URL+"js/client.js?cloud="+BXC_CLOUD.cloud+'"><\/script>':""}`:`[boxcoin id="${e}"]`,i=i.replace("//checkout","/checkout"),U(t.e[0]).html(0==a?`<a href="${i}" target="_blank">${i}</a>`:i.replace(/</g,"&lt;")),U(t.e[1]).data("text",window.btoa(i))}else U(t.e[0]).html(""),U(t.e[1]).data("text","")}},get:function(e,t=!1){for(var a=0;a<I.length;a++)if(e==I[a].id)return t?(I.splice(a,1),!0):I[a];return!1}},G={active_element:!1,card:function(e,t=!1){var a=m.find(".bxc-info-card");a.removeClass("bxc-info-card-error bxc-info-card-warning bxc-info-card-info"),t?"error"==t?a.addClass("bxc-info-card-error"):a.addClass("bxc-info-card-info"):clearTimeout(x),a.html(n(e)),x=setTimeout(()=>{a.html("")},5e3)},error:function(t,a=!1){window.scrollTo({top:0,behavior:"smooth"}),m.find(".bxc-info").html(t),a&&e(a,!1)},confirm:function(e,t,a,i=!1){if(i)return t();BOXCoin.lightbox(n("Are you sure?"),'<div class="bxc-text">'+n(e)+"</div>",[["bxc-confirm-btn","Confirm"],["bxc-cancel-btn","Cancel"]],"confirm"),B=[t,a]},balance:function(a){if(!e(a)){let i=m.find("#bxc-table-balances tbody");if(!i.e.length)return;i.html()||a.addClass("bxc-loading-first"),t("get-balances",{},t=>{let n="",c=t.balances;m.find("#bxc-balance-total").html(`${BXC_CURRENCY} ${t.total}`);for(var o in c){n+=`<tr data-cryptocurrency="${o}"><td><div class="bxc-flex"><img src="${o in t.token_images?t.token_images[o]:`${BXC_URL}media/icon-${o}.svg`}" /> ${c[o].name}${BOXCoin.network(o,!0,!0)}</div></td><td><div class="bxc-balance bxc-title">${c[o].amount} ${BOXCoin.baseCode(o).toUpperCase()}</div><div class="bxc-text">${BXC_CURRENCY} ${c[o].fiat}</div></td></tr>`}n?i.html(n):i.parent().html('<p class="bxc-text">Add your addresses from the Settings area.</p>'),a.removeClass("bxc-loading-first"),e(a,!1)})}},explorer:function(e,t,a="address"){let i="",n="address"==a?"address":["ltc","usdt_tron"].includes(e)?"transaction":"tx";switch(0===t.toString().indexOf("cus_")&&(e="stripe",n=t),e){case"btc":i=BXC_ADMIN_SETTINGS.testnet_btc?"https://mempool.space/testnet/{R}/{V}":"https://www.blockchain.com/btc/{R}/{V}";break;case"eth":i="https://www.blockchain.com/eth/{R}/{V}";break;case"doge":i="https://dogechain.info/{R}/{V}";break;case"link":case"bat":case"shib":case"usdc":case"usdt":i="https://etherscan.io/{R}/{V}"+("address"==a?"":"#tokentxns");break;case"usdt_tron":i="https://tronscan.org/#/{R}/{V}";break;case"algo":i="https://algoexplorer.io/{R}/{V}";break;case"stripe":i="https://dashboard.stripe.com/"+(BXC_ADMIN_SETTINGS.stripe_test_mode?"test/":"")+"customers/{R}";break;case"verifone":i="https://secure.2checkout.com/cpanel/order_info.php?refno={V}";break;case"usdt_bsc":case"busd":case"bnb":i="https://bscscan.com/{R}/{V}";break;case"ltc":i="https://blockchair.com/litecoin/{R}/{V}";break;case"bch":i="https://www.blockchain.com/bch/{R}/{V}";break;case"xrp":n="address"==a?"accounts":"transactions",i="https://livenet.xrpl.org/{R}/{V}";break;case"sol":n="address"==a?"account":"tx",i="https://solscan.io/{R}/{V}";break;case"xmr":if("address"==a)return!1;i="https://blockchair.com/monero/transaction/{V}";break;default:let c=BOXCoin.network(e,"code");"eth"==c&&(i="https://etherscan.io/{R}/{V}"+("address"==a?"":"#tokentxns")),"bsc"==c&&(i="https://bscscan.com/{R}/{V}")}return i.replace("{R}",n).replace("{V}",t)},datepicker:function(e){R&&R.destroy(),(R=new DateRangePicker(U(e).parent().e[0],A)).datepickers[0].show()},repeater:{add:function(e,t=!0,a=!1){let i=U(e).parent(),n=i.find("[data-type], hr"),c='<div class="bxc-repater-line"><hr /><i class="bxc-icon-close"></i></div>';a=a||parseInt(U(e).data("index"));for(var o=0;o<n.e.length&&"HR"!=n.e[o].nodeName;o++){let e=U(n.e[o]).attr("id");c+=n.e[o].outerHTML.replace(e,e+"-"+a)}if(!t)return c;c+=e.outerHTML.replace('"'+a+'"','"'+(a+1)+'"'),e.remove(),i.append(c)},open:function(e,t){t.find(".bxc-btn-repater").e.forEach(t=>{let a=2,i="",n=U(t.parentElement).find("[id]").attr("id");for(;n+"-"+a in e;)i+=this.add(t,!1,a),a++;i&&(i+=t.outerHTML.replace('"2"','"'+a+'"'),U(t).parent().append(i),t.remove())})},delete:function(e){let t=U(e).hasClass("bxc-first-repeater-item"),a=U(e).parent(),i=Array.from(a.parent().e[0].childNodes),n=i.indexOf(a.e[0])+1,c=a.parent().find(".bxc-btn-repater"),s=parseInt(c.data("index"))-1,d=!1;for(e=n;e<i.length;e++){let a=U(i[e]);if(d||a.hasClass("bxc-repater-line")||a.hasClass("bxc-btn-repater")){d=!0;let e=a.attr("id"),t=parseInt(!(!e||!e.includes("-"))&&e.substr(e.lastIndexOf("-")+1));t&&a.attr("id",e.replace("-"+t,"-"+(t-1)))}else t?o(r(a),""):a.remove()}c.data("index",s<2?2:s),t||a.remove()}}};window.BXCTransactions=E,window.BXCCheckout=M,window.BXCAdmin=G,document.addEventListener("DOMContentLoaded",()=>{if(b=U(document.body),(m=b.find(".bxc-main")).e.length){if(v=m.find("main > div"),k=v.e[0],_=m.find("#bxc-table-transactions"),w=b.find("#bxc-upload-form input"),g="undefined"!=typeof BXC_WP,BOXCoin.cookie("BXC_LOGIN")&&!m.hasClass("bxc-installation")){C="transactions",s()||E.print(()=>{e(k,!1)}),localStorage.getItem("bxc-cron")!=N.getDate()&&(t("cron",{domain:BXC_URL}),localStorage.setItem("bxc-cron",N.getDate()));let a=document.getElementsByTagName("textarea");for(let e=0;e<a.length;e++)a[e].setAttribute("style","height:"+a[e].scrollHeight+"px;overflow-y:hidden;"),a[e].addEventListener("input",h,!1);G.balance(m.find('main > [data-area="balance"]'))}_.on("click","tr",function(e){if(["A","I","LI"].includes(e.target.nodeName))return;let t=U(this).data("hash");if(t){let e=U(this).data("cryptocurrency"),a=G.explorer(e,t,"tx");a&&window.open(a)}}),m.on("click","#bxc-filters",function(){k.find(".bxc-nav-filters").toggleClass("bxc-active")}),m.on("click",".bxc-filter-date",function(){L?R||G.datepicker(this):(U.load(BXC_URL+"vendor/datepicker/datepicker.min.css",!1),U.load(BXC_URL+"vendor/datepicker/datepicker.min.js",!0,()=>{BXC_LANG?(A.language=BXC_LANG,U.load(`${BXC_URL}vendor/datepicker/locales/${BXC_LANG}.js`,!0,()=>{L=!0,G.datepicker(this)})):(L=!0,G.datepicker(this))}))}),m.on("click",".bxc-filter-status li, .bxc-filter-cryptocurrency li, .bxc-filter-checkout li, .datepicker-cell, .datepicker .clear-btn",function(){setTimeout(()=>{"transactions"==C?E.query():BXCAdminShop.analytics(R)},100)}),m.on("click","#bxc-download-transitions",function(){e(this)||E.download(t=>{window.open(t),e(this,!1)})}),_.on("click",".bxc-transaction-menu-btn:not(.bxc-loading)",function(){let e=U(this).hasClass("bxc-active"),t=U(this.closest("tr")),i=`<ul class="bxc-transaction-menu bxc-ul"><li data-value="details">${n("Details")}</li>`,c=t.data("status"),o=t.data("cryptocurrency");if(a(_.find(".bxc-transaction-menu-btn"),!1),_.find(".bxc-transaction-menu").remove(),!e){a(this),"P"==c&&t.data("amount")?i+=`<li data-value="payment-link">${n("Payment link")}</li>`:BXC_ADMIN_SETTINGS.invoice&&(i+=`<li data-value="invoice">${n("Invoice")}</li>`),["C","X"].includes(c)&&(BXC_REFUNDS.includes("coinbase")&&["btc","eth","xrp","usdt","usdc","busd","bnb","link","doge","shib","ltc","algo","bat","bch","sol"].includes(o)||BXC_REFUNDS.includes("btc")&&"btc"==o||BXC_REFUNDS.includes("eth")&&["eth","usdt","usdc","link","shib","bat"].includes(o))&&(i+=`<li data-value="refund">${n("Issue a refund")}</li>`);for(var r in D)r!=c&&(i+=`<li data-value="${r}">${n("Mark as "+D[r].toLowerCase())}</li>`);U(this).parent().append(i+`<li data-value="delete">${n("Delete")}</li></ul>`)}}),_.on("click",".bxc-transaction-menu li",function(){let c=U(this.closest("tr")),o=c.find(".bxc-transaction-menu-btn");if(e(o))return;let r=U(this).data("value"),s=c.data("id");if(["C","P","R"].includes(r)){let a;if("C"==r)switch(c.data("type")){case"2":case"1":a="The transaction will be processed, and any user purchase associated with it will be processed and finalized.";break;case"3":a="The exchange will be processed and the amount to be exchanged will be automatically sent to the user."}G.confirm(a,()=>{t("update-transaction",{transaction_id:s,values:{status:r}},t=>{!0===t?c.data("status",r).find(".bxc-td-status span").attr("class","bxc-status-"+r).html(n(D[r])):i(t,"error"),e(o,!1)})},o,!a)}if("invoice"==r&&t("encryption",{string:s},t=>{window.open((BXC_CLOUD.custom_domain?BXC_CLOUD.custom_domain:BXC_URL)+(BXC_ADMIN_SETTINGS.url_rewrite_invoice?BXC_ADMIN_SETTINGS.url_rewrite_invoice:"pay.php?invoice=")+t+f()),e(o,!1)}),"payment-link"==r&&t(r,{transaction_id:s},t=>{window.open(t+f(t)),e(o,!1)}),"details"==r){let a='<div class="bxc-text-list bxc-transaction-details-list">',i=[["ID","id"],["Checkout","data-title"],["Hash","data-hash"],["Time","time"],["Notes","data-notes"],["Status","status"],["From","from"],["To","to"],["Amount","amount"],["VAT","data-vat"],["License key","data-license-key"]];for(var d=0;d<i.length;d++){let e=i[d][1],t=e.includes("data")?c.attr(e):c.find(".bxc-td-"+e).html();if(t){if(t=t.trim(),"data-hash"==e)t=`<a href="${G.explorer(c.data("cryptocurrency"),t,"tx")}" target="_blank">${t}</a>`;else if("data-title"==e)t=t+" - ID "+c.data("checkout-id");else if("data-notes"==e)t=t.replaceAll("\\r\\n","\n").replaceAll("\\r","\n").replaceAll("\\n","<br>").replaceAll("\n","<br>");else if("data-license-key"==e){let e=c.attr("data-license-key-status").trim();1!=e&&(t='<div class="bxc-label-code">'+t+'</div><div class="bxc-label">'+n("Disabled")+"</div>"),t+=`<div id="bxc-btn-license-key-status" class="bxc-btn-text bxc-underline" data-status="${e}" data-id="${c.data("id")}">${n(1==e?"Disable":"Enable")}</div>`}a+=`<div data-name="${e}"><div>${n(i[d][0])}</div><div>${t}</div></div>`}}BXC_ADMIN_SETTINGS.apps.includes("shop")&&(c.data("discount-code")&&(a+=`<div><div>${n("Discount code")}</div><div>${c.data("discount-code")}</div></div>`),c.data("customer-id")&&(a+=`<div data-name="customer"><div>${n("Customer details")}</div><div class="bxc-loading"></div></div>`,t("get-customer",{customer_id:c.data("customer-id")},e=>{let t=e.first_name+(e.last_name?" "+e.last_name:"");b.find('#bxc-lightbox [data-name="customer"] .bxc-loading').html((t?t+" - ":"")+"ID "+e.id+(e.email?"<br>"+e.email:"")+(e.phone?"<br>"+e.phone:"")+(e.country?"<br>"+e.country+" "+e.country_code:"")).removeClass("bxc-loading")}))),BOXCoin.lightbox("Transaction details",a),e(o,!1)}"refund"==r&&(i(n("Sending refund in 5 seconds.")+' <span id="cancel-refund">'+n("Cancel")+"</span>","info"),y=setTimeout(()=>{t(r,{transaction_id:s},t=>{if(!0===t.status){c.data("status",r).find(".bxc-td-status span").attr("class","bxc-status-R").html(n(D.R)),i(t.message);let e=m.find(".bxc-info-card a");e.attr("href",G.explorer(c.data("cryptocurrency"),e.data("hash"),"tx"))}else i(t.message,"error");e(o,!1)})},5e3)),"delete"==r&&G.confirm("The conversation will be deleted permanently.",()=>{t("delete-transaction",{transaction_id:s},t=>{!0===t?c.remove():i(t,"error"),e(o,!1)})},o),c.find(".bxc-ul").remove(),a(o,!1)}),m.on("click","#cancel-refund",function(){clearTimeout(y),e(_.find(".bxc-loading"),!1)}),m.on("click","#bxc-request-payment",function(){let e="",t=[["price","Price","number"],["currency","Currency code","text"],["pay","Cryptocurrency code","text"],["redirect","Redirect URL","url"],["note","Notes","text"]];for(var a=0;a<t.length;a++)e+=`<div class="bxc-input"><span>${n(t[a][1])}</span><input data-url-attribute="${n(t[a][0])}" type="${n(t[a][2])}"></div>`;BOXCoin.lightbox("Create a payment request",e+`<div id="bxc-create-payment-link" class="bxc-btn">${n("Create payment link")}</div>`)}),b.on("click","#bxc-create-payment-link",function(){let e=U(this).parent().find("input").e,t="",a=f();for(var i=0;i<e.length;i++){let a=U(e[i]).data("url-attribute"),n=U(e[i]).val();n&&("redirect"!=a&&"note"!=a||(n=encodeURIComponent(n)),t+="&"+a+"="+n)}t&&(t=`${BXC_CLOUD.custom_domain?BXC_CLOUD.custom_domain:BXC_URL}${BXC_ADMIN_SETTINGS.url_rewrite_checkout?BXC_ADMIN_SETTINGS.url_rewrite_checkout:"pay.php?checkout_id="}custom-${Math.floor(Date.now()/1e3)}${a}${t&&BXC_ADMIN_SETTINGS.url_rewrite_checkout&&!a?"?"+t.substring(1):t}`,U(this).parent().find("#bxc-payment-request-url-box").remove(),U(this).insert(`<div id="bxc-payment-request-url-box" class="bxc-input"><a href="${t}" target="_blank">${t.replace(/&/g,"&amp")}</a><i class="bxc-icon-copy bxc-clipboard" data-text="${window.btoa(t)}"></i></div>`))}),b.on("click","#bxc-btn-license-key-status",function(){let e=1==U(this).data("status")?2:1,a=U(this).data("id");t("update-license-key-status",{transaction_id:a,status:e},()=>{U(this).html(n(1==e?"Disable":"Enable")).data("status",e),_.find(`[data-id="${a}"]`).data("license-key-status",e),U(this).parent().find(".bxc-label").remove(),1!=e&&U(this).insert('<div class="bxc-label">'+n("Disabled")+"</div>")})}),m.on("click","#bxc-create-checkout, #bxc-table-checkouts td",function(){let e=!1;if(m.addClass("bxc-area-create-checkout"),U(this).is("td")){let a=U(this).parent().data("checkout-id");e=M.get(a),k.data("checkout-id",a);for(var t in e){o(r(k.find(`#bxc-checkout-${t}`)),e[t])}M.embed(e.slug?e.slug:a)}else r(k).e.forEach(e=>{o(e,"")}),k.find("#bxc-checkout-type select").val("I"),k.data("checkout-id",""),M.embed();BXC_ADMIN_SETTINGS.apps.includes("shop")&&BXCAdminShop.checkoutOpen(e,k)}),m.on("click","#bxc-checkouts-list",function(){m.removeClass("bxc-area-create-checkout"),k.data("checkout-id","")}),m.on("click","#bxc-save-checkout",function(){if(e(this))return;let a=!1,n={},r=k.find(".bxc-input"),s=k.data("checkout-id");if(m.find(".bxc-info").html(""),r.removeClass("bxc-error"),r.e.forEach(e=>{let t=U(e).attr("id"),i=U(e).find("input, select"),c=o(i);!c&&i.e.length&&i.e[0].hasAttribute("required")&&(a=!0,U(e).addClass("bxc-error")),n[t.replace("bxc-checkout-","")]=c}),a)c("Fields in red are required.",this);else{if(s&&(n.id=s),BXC_ADMIN_SETTINGS.apps.includes("shop")&&(n=BXCAdminShop.checkoutSave(n,k)),BXC_ADMIN_SETTINGS.url_rewrite_checkout&&!n.slug&&n.title){let e=function(e){e=(e=e.trim()).toLowerCase();for(var t="åàáãäâèéëêìíïîòóöôùúüûñç·/_,:;",a=0,i=t.length;a<i;a++)e=e.replace(new RegExp(t.charAt(a),"g"),"aaaaaaeeeeiiiioooouuuunc------".charAt(a));return e.replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").replace(/^-+/,"").replace(/-+$/,"").replace(/ /g,"")}(n.title);n.slug=e}for(var d=0;d<I.length;d++)I[d].slug!=n.slug||n.id&&n.id==I[d].id||(n.slug="");k.find("#bxc-checkout-slug input").val(n.slug),t("save-checkout",{checkout:JSON.stringify(n)},t=>{e(this,!1),Number.isInteger(t)?(n.id=t,k.data("checkout-id",t),k.find("#bxc-table-checkouts tbody").append(M.row(n)),M.embed(n.slug?n.slug:t),I.push(n),i("Checkout saved successfully")):!0===t?(M.get(s,!0),I.push(n),k.find(`tr[data-checkout-id="${s}"]`).replace(M.row(n)),M.embed(n.slug?n.slug:n.id),i("Checkout saved successfully")):c(t,this.closest("form"))})}}),m.on("click","#bxc-delete-checkout",function(){if(e(this))return;let a=k.data("checkout-id");t("delete-checkout",{checkout_id:a},()=>{e(this,!1),k.data("checkout-id",""),k.find(`tr[data-checkout-id="${a}"]`).remove(),k.find("#bxc-checkouts-list").e[0].click(),i("Checkout deleted","error")})}),m.on("click","#checkout-downloads .bxc-repater-line i",function(){let e=U(this).parent().next().find("input").val();e&&t("delete-file",{file_name:e,folder:"checkout/"})}),m.on("click","#checkout-downloads input",function(){U(this).val()&&window.open(BXC_CLOUD.aws?BXC_CLOUD.aws+U(this).val():BXC_URL+"uploads"+(BXC_CLOUD?BXC_CLOUD.path_part:"")+"/checkout/"+U(this).val())}),m.on("click","#bxc-save-settings",function(){if(e(this))return;let a={};m.find('[data-area="settings"]').find('.bxc-input[id]:not([data-type="multi-input"]),[data-type="multi-input"] [id]').e.forEach(e=>{a[U(e).attr("id")]=o(r(e))}),t("save-settings",{settings:JSON.stringify(a)},t=>{i(!0===t?"Settings saved":t,!0!==t&&"error"),e(this,!1)})}),m.on("click","#update-btn a",function(a){if(!e(this))return t("update",{domain:BXC_URL},t=>{e(this,!1);let a=!1,n=!0;if("string"==typeof t)i(l(t),"error");else{for(var c in t)!0!==t[c]&&""!==t[c]&&"latest-version-installed"!==t[c]?a=!0:!0===t[c]&&(n=!1);n?i("You have the latest version!"):a?i(l(JSON.stringify(t)),"error"):(i("Update completed. Reload in progress..."),setTimeout(()=>{location.reload()},500))}}),a.preventDefault(),!1}),m.on("click","#email-test-btn a",function(a){if(!e(this))return t("email-test",{},t=>{i(!0===t?"Email successfully sent.":t),e(this,!1)}),a.preventDefault(),!1}),m.on("click",".bxc-btn-repater",function(){G.repeater.add(this)}),m.on("click",".bxc-repater-line i",function(){G.repeater.delete(this)}),m.on("click","#two-fa-pairing a",function(e){return t("2fa",{},e=>{window.open(e)}),e.preventDefault(),!1}),m.on("click","#two-fa-validation a",function(e){let a=m.find("#two-fa-code input"),n=a.val().trim();return n&&t("2fa",{code:n},e=>{i(!0===e?"2FA activated!":"Invalid code.",!0===e?"info":"error"),a.val("")}),e.preventDefault(),!1}),O&&m.on("click",".bxc-icon-menu",function(){let e=U(this).parent();a(e,!e.hasClass("bxc-active"))}),m.on("click","#bxc-submit-installation",function(){if(e(this))return;let a=!1,i={},n=window.location.href.replace("/admin","").replace(".php","").replace(/#$|\/$/,""),o=m.find(".bxc-input");if(o.removeClass("bxc-error"),m.find(".bxc-info").html(""),o.e.forEach(e=>{let t=U(e).attr("id"),n=U(e).find("input"),c=n.val().trim();!c&&n.e[0].hasAttribute("required")&&(a=!0,U(e).addClass("bxc-error")),i[t]=c}),a){a="Username, password and the database details are required.";let e=i.password;return e&&(e.length<8?a="Minimum password length is 8 characters.":e!=i["password-check"]&&(a="The passwords do not match.")),void c(a,this)}n.includes("?")&&(n=n.substr(0,n.indexOf("?"))),i.url=n+"/",t("installation",{installation_data:JSON.stringify(i)},e=>{!0===e?location.reload():c(e,this)})}),m.on("click",".bxc-nav > div",function(){d(U(this))}),m.on("click","#bxc-submit-login",function(){e(this)||t("login",{username:m.find("#username input").val().trim(),password:m.find("#password input").val().trim(),code:m.find("#two-fa input").val().trim()},t=>{let a=!1;if("ip-ban"==t&&(a="Too many login attempts. Please retry again in a few hours."),"2fa"==t&&(a="Invalid verification code."),t||(a="Invalid username or password."),a)return m.find(".bxc-info").html(n(a)),e(this,!1);BOXCoin.cookie("BXC_LOGIN",t,365,"set"),location.reload()})}),m.on("click","#bxc-logout",function(){BOXCoin.cookie("BXC_LOGIN",!1,!1,"delete"),BOXCoin.cookie("BXC_CLOUD",!1,!1,"delete"),location.reload()}),m.on("click","#bxc-card",function(){U(this).html("")}),m.on("input",".bxc-search-input",function(){BOXCoin.search(this,(e,t)=>{"transactions"==C&&E.query(t,e)})}),m.on("click","#bxc-table-balances tr",function(){let e=U(this).data("cryptocurrency");for(var t=0;t<BXC_ADDRESS[e].length;t++){let a=G.explorer(e,BXC_ADDRESS[e][t]);a&&window.open(a)}}),window.onscroll=function(){k&&(document.documentElement||document.body.parentNode||document.body).scrollTop+window.innerHeight==U.documentHeight()&&S&&"transactions"==C&&(U(k).find("> .bxc-loading-global").e.length||(E.print(()=>{window.scrollTo(0,document.body.scrollHeight-800),U(k).find("> .bxc-loading-global").remove()}),U(k).append('<div class="bxc-loading-global bxc-loading"></div>')))},window.onpopstate=function(){s()},document.addEventListener("click",function(e){G.active_element&&!G.active_element.contains(e.target)&&(a(G.active_element,!1),G.active_element=!1)}),m.on("click",'[data-type="upload-file"] > div',function(){e(this,"check")||(w.val(""),w.e[0].click(),$=this)}),w&&w.e.length&&w.e[0].addEventListener("change",function(a){let i=this.files;e($);for(var o=0;o<i.length;o++){let a=i[o],r=a.size/1048576,s=BXC_ADMIN_SETTINGS.max_file_size,d=new FormData,l=U($).parent().data("upload-target");if(r>s)return c(n("Maximum upload size is {R}MB. File size: {R2}MB.").replace("{R}",s).replace("{R2}",r.toFixed(2)),this);d.append("file",a),fetch(BXC_URL+"upload.php?target="+l,{method:"POST",body:d}).then(e=>e.json()).then(function(a){if(e($,!1),a[0]){let e=U($).parent(),i=e.find("input");i.val()&&t("delete-file",{file_name:i.val(),folder:"checkout-file"==l?"checkout/":""}),i.val(a[2]),e.hasClass("bxc-upload-image")&&(e.find(".bxc-btn-icon").e[0].style.backgroundImage='url("'+a[1]+'")')}else c(a[1]);BOXCoin.event("BXCUpload",a)})}this.value=""}),m.on("click",".bxc-upload-image > i",function(){let e=U(this).prev();e.attr("style",""),e.prev().val()&&t("delete-file",{file_name:e.prev().val()}),e.prev().val("")}),b.on("click","#bxc-confirm-btn, #bxc-cancel-btn",function(){"bxc-cancel-btn"==U(this).attr("id")?B[1]&&e(B[1],!1):B[0](),BOXCoin.lightboxClose()})}})}();