/*

===================================================================
CLOUD ADMIN CSS FILE
===================================================================

*/
.bxc-cloud-box {
  margin: 30px auto !important; }
  .bxc-cloud-box .bxc-bottom {
    justify-content: flex-start;
    display: flex;
    align-items: center;
    padding-top: 50px; }
    .bxc-cloud-box .bxc-bottom div + div {
      margin-left: 15px; }
    .bxc-cloud-box .bxc-bottom .bxc-text {
      font-size: 15px;
      line-height: 25px; }
  .bxc-cloud-box #bxc-forgot-password {
    margin: 10px 0 0 0 !important;
    text-align: right;
    font-size: 13px; }
  .bxc-cloud-box #bxc-cancel-reset-password, .bxc-cloud-box #bxc-forgot-password {
    cursor: pointer; }
    .bxc-cloud-box #bxc-cancel-reset-password:hover, .bxc-cloud-box #bxc-forgot-password:hover {
      color: #ca3434;
      transition: all 0.4s; }

#bxc-registration-box, #bxc-login-box {
  color: #2bb0ba;
  cursor: pointer;
  margin-left: 5px !important; }

.disclaimer {
  text-align: center;
  max-width: 600px;
  display: block;
  margin: 0 auto 30px auto;
  font-size: 13px;
  line-height: 25px;
  font-family: "Boxcoin"; }
  .disclaimer a {
    color: #2bb0ba;
    text-decoration: none; }

.bxc-cloud-box:not(.active) {
  display: none !important; }

.bxc-errors-area {
  color: #ca3434;
  margin: 30px 0 0 0;
  font-size: 15px;
  line-height: 25px; }

input.bxc-error {
  border: 1px solid #ca3434;
  box-shadow: 0 0 5px rgba(202, 52, 52, 0.25); }

#bxc-payment-box {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  grid-gap: 20px;
  max-width: 600px;
  margin-top: 30px; }
  #bxc-payment-box > a {
    color: #000;
    text-decoration: none;
    border: 1px solid #d4d4d4;
    border-radius: 4px;
    padding: 20px;
    font-weight: 500;
    letter-spacing: .3px;
    cursor: pointer;
    text-align: center;
    transition: all .3s; }
    #bxc-payment-box > a:hover {
      border-color: #2bb0ba;
      color: #2bb0ba; }

#bxc-table-payments {
  margin-top: 30px;
  max-width: 600px; }
  #bxc-table-payments img {
    width: 20px;
    display: block;
    margin: 0 0 0 auto;
    transition: all .3s; }
    #bxc-table-payments img:hover {
      opacity: .6; }
  #bxc-table-payments th:first-child, #bxc-table-payments td:first-child {
    width: 20px;
    overflow: hidden; }
  #bxc-table-payments th:last-child, #bxc-table-payments td:last-child {
    text-align: right; }

.bxc-credits-box a {
  text-decoration: none;
  font-size: 12px;
  padding-left: 5px;
  color: #969696; }
  .bxc-credits-box a:hover {
    color: #2bb0ba; }

#bxc-checkout-embed-code div, #bxc-checkout-payment-link div {
  margin-top: 10px;
  max-width: calc(100% - 80px);
  height: 20px;
  white-space: nowrap;
  overflow-x: scroll;
  padding-right: 65px; }
  #bxc-checkout-embed-code div::-webkit-scrollbar, #bxc-checkout-payment-link div::-webkit-scrollbar {
    height: 5px; }
  #bxc-checkout-embed-code div::-webkit-scrollbar-track, #bxc-checkout-payment-link div::-webkit-scrollbar-track {
    background: #f1f1f1; }
  #bxc-checkout-embed-code div::-webkit-scrollbar-thumb, #bxc-checkout-payment-link div::-webkit-scrollbar-thumb {
    background: #ced6db;
    border-radius: 6px; }
  #bxc-checkout-embed-code div::-webkit-scrollbar-thumb:hover, #bxc-checkout-payment-link div::-webkit-scrollbar-thumb:hover {
    background: #A0A0A0; }
#bxc-checkout-embed-code i, #bxc-checkout-payment-link i {
  top: 38px; }

[data-checkout-id] #bxc-checkout-embed-code, [data-checkout-id] #bxc-checkout-payment-link {
  display: block !important; }

#reset-password {
  font-size: 14px; }

#bxc-account-list {
  margin-top: 30px;
  max-width: 600px; }
  #bxc-account-list .bxc-input {
    align-items: center;
    margin-bottom: 15px; }
    #bxc-account-list .bxc-input span {
      max-width: 100px;
      flex-shrink: 0; }
  #bxc-account-list .bxc-input + .bxc-input {
    margin-top: 0;
    border-top: none; }

#account-api-key {
  cursor: text; }

#api-keys, #openexchangerates-app-id, #update, #js-admin, #envato-purchase-code, #minify, #url-rewrite, #shop-envato-purchase-code, #api-key, #shop-envato-purchase-code {
  display: none !important; }

#bxc-super-save-customer {
  margin-top: 30px; }

.bxc-cloud-disabled {
  position: relative; }
  .bxc-cloud-disabled input, .bxc-cloud-disabled span, .bxc-cloud-disabled select, .bxc-cloud-disabled .bxc-setting-content p {
    opacity: .5; }
  .bxc-cloud-disabled input, .bxc-cloud-disabled select {
    transition: all .5s;
    background-color: #FFF !important; }
  .bxc-cloud-disabled:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    background-color: white;
    z-index: 9; }
  .bxc-cloud-disabled:after {
    content: "Only available in PHP and WP versions";
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    right: 0;
    z-index: 11;
    text-align: center;
    font-weight: 500;
    font-size: 13px;
    letter-spacing: .3px;
    white-space: nowrap;
    opacity: 0;
    color: #2bb0ba; }
  .bxc-cloud-disabled[data-type="checkbox"]:after {
    top: 10px; }
  .bxc-cloud-disabled[data-type="checkbox"]:before {
    background-color: white; }
  .bxc-cloud-disabled:hover:before, .bxc-cloud-disabled:hover:after {
    opacity: 1; }

.bxc-credit-disabled:after {
  content: "To access this feature, a minimum credit purchase of 50 USD is required."; }

/*
     
# RESPONSIVE
==========================================================

*/
@media (max-width: 428px) {
  .bxc-cloud-box {
    border: none; }
    .bxc-cloud-box .bxc-bottom, .bxc-cloud-box .bxc-bottom > div {
      display: block;
      text-align: center;
      margin-bottom: 5px; }
      .bxc-cloud-box .bxc-bottom.bxc-btn, .bxc-cloud-box .bxc-bottom > div.bxc-btn {
        margin-bottom: 15px; } }

/*# sourceMappingURL=admin.css.map */
