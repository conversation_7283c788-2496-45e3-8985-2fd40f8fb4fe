
/*
* 
* ===================================================================
* CLOUD SUPER
* ===================================================================
*
*/

var _ = window._query;
var URL = document.location.href;

document.addEventListener('DOMContentLoaded', () => {
    let box_login = _('#bxc-super-login');
    let box_super = _('#bxc-super');

    if (box_login.e.length) {
        box_login.on('click', '#bxc-super-submit-login', function (e) {
            let email = box_login.find('#email input').val();
            let password = box_login.find('#password input').val();
            let errors_area = box_login.find('.bxc-info');
            if (!email || !password || loading(this)) return;
            BOXCoin.ajax('cloud', { action: 'super-login', arguments: { email: email, password: password } }, (response) => {
                if (response === false) {
                    errors_area.html(bxc_('Invalid email or password.'));
                } else {
                    BOXCoin.cookie('BXC_SUPER', response, 365, 'set');
                    location.reload();
                }
                loading(this, false);
            });
            e.preventDefault();
            return false;
        });
    }

    if (box_super.e.length) {
        let box_customers_table = box_super.find('#bxc-table-customers');
        BOXCoin.cookie('BXC_LOGIN', false, false, 'delete');
        BOXCoin.cookie('BXC_CLOUD', false, false, 'delete');
        box_customers_table.on('click', '[data-id]', function () {
            activate(_('#bxc-lightbox-loading'));
            let customer_id = _(this).attr('data-id');
            BOXCoin.ajax('cloud', { action: 'super-get-customer', arguments: { customer_id: customer_id } }, (response) => {
                let code = '';
                for (var key in response) {
                    code += `<div id="${key}" ${key == 'credit' ? 'data-credit="' + response[key] + '" ' : ''} class="bxc-input"><span>${slugToString(key)}</span><input type="text" value="${response[key]}" ${key == 'credit' ? '' : 'disabled'}></div>`;
                }
                BOXCoin.lightbox(response.email, code + '<div id="bxc-super-save-customer" data-customer-id="' + customer_id + '" data-token="' + response.token + '" class="bxc-btn">Save customer</div>');
                BOXCoin.event('SuperCustomerOpened', response);
            });
        });

        box_super.on('click', '#bxc-super-save-customer', function () {
            let credit = box_super.find('#bxc-lightbox #credit');
            let credit_value = credit.find('input').val();
            if (credit.attr('data-credit') != credit_value) {
                if (loading(this)) return;
                BOXCoin.ajax('cloud', { action: 'super-save-customer', arguments: { token: _(this).attr('data-token'), details: { credit: credit_value } } }, (response) => {
                    loading(this, false);
                    BOXCoin.lightboxClose(); 
                    if (response !== true) alert(response);
                });
            } 
        });
    }

    function slugToString(string) {
        string = string.replace(/_/g, ' ').replace(/-/g, ' ');
        return string.charAt(0).toUpperCase() + string.slice(1);
    }
});
 