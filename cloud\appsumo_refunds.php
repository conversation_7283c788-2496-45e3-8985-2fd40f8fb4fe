<?php

/*
 *
 * ===================================================================
 * APPSUMO REFUNDS
 * ===================================================================
 *
 */


if (!empty($_POST['appsumo_codes'])) {
    require('../functions.php');
    require('functions.php');
    if (password_verify($_POST['psw'], '$2y$10$sn9EZQbKEgGInfzgaKQ51.sXk2/5/fq1aBvFwbv4z6/zoG32H.z7O')) {
        $codes = preg_split("/\r\n|\n|\r/", $_POST['appsumo_codes']);
        $payment_history = db_get('SELECT A.db_name, B.id, B.transaction_id FROM users A, payment_history B WHERE A.id = B.user_id', false);
        $response = '';
        for ($i = 0; $i < count($payment_history); $i++) {
            $transaction = $payment_history[$i];
            for ($y = 0; $y < count($codes); $y++) {
                $code = trim($codes[$y]);
                if ($code) {
                    $transaction_id = $transaction['transaction_id'];
                    if (strpos($transaction_id, $code) !== false && strpos($transaction_id, $code . '-refunded') === false) {
                        db_query('UPDATE ' . $transaction['db_name'] . '.bxc_settings SET value = 0 WHERE name = "credit_balance" LIMIT 1');
                        db_query('UPDATE payment_history SET transaction_id = "' .  db_escape(str_replace($code, $code . '-refunded', $transaction_id)) . '" WHERE id = ' . $transaction['id']);
                        $response .= '<br>' . $code;
                    }
                }
            }
        }
        echo 'The following codes were found and succesfully deleted: <br>' . $response;
    } else echo 'Wrong password';
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no" />
    <title>
        AppSumo Refunds | Boxcoin
    </title>
    <link rel="stylesheet" href="../css/admin.css" media="all" />
    <link rel="shortcut icon" type="image/svg" href="../media/icon.svg" />
    <style>
    .bxc-input {
        align-items: center;
    }

    .bxc-input input {
        margin-left: 15px !important;
    }

    .bxc-top > img {
        width: 250px;
        margin: 0 0 60px 0;
    }

    .bxc-top + .bxc-bottom {
        padding-top: 0;
    }

    </style>
</head>
<body>
    <div class="bxc-main bxc-cloud-registration bxc-box">
        <form method="post">
            <div class="bxc-input">
                <span>Password</span>
                <input id="psw" name="psw" type="password">
            </div>
            <div class="bxc-input">
                <span>Appsumo refunded codes separted by break lines</span>
                <textarea id="appsumo_codes" name="appsumo_codes"></textarea>
            </div>
            <div class="bxc-bottom">
                <input type="submit" class="bxc-btn" value="Set $1 credit for refunded codes" />
            </div>
        </form>
    </div>
</body>
</html>